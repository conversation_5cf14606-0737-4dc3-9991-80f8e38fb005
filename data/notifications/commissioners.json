[{"commissionerId": 32, "notifications": [{"id": "task-2-301", "type": "task_submission", "title": "<PERSON><PERSON><PERSON> submitted a task", "message": "\"Hero section design\" is awaiting your review", "timestamp": "2025-07-12T11:51:51.676Z", "isRead": true, "userId": 31, "project": {"id": 301, "title": "Lagos Parks Services website re-design"}, "isFromNetwork": true}, {"id": "task-101-311", "type": "task_submission", "title": "<PERSON><PERSON><PERSON> submitted a task", "message": "\"User authentication flow design\" is awaiting your review", "timestamp": "2025-07-08T11:39:37.155Z", "isRead": false, "project": {"id": 311, "title": "Lagos Parks Mobile App Development"}, "isFromNetwork": true, "userId": 31}, {"id": "task-102-311", "type": "task_submission", "title": "<PERSON><PERSON><PERSON> submitted a task", "message": "\"Park booking interface mockups\" is awaiting your review", "timestamp": "2025-07-08T18:12:23.463Z", "isRead": true, "project": {"id": 311, "title": "Lagos Parks Mobile App Development"}, "isFromNetwork": true, "userId": 31}, {"id": "task-103-311", "type": "task_submission", "title": "<PERSON><PERSON><PERSON> submitted a task", "message": "\"Maintenance reporting feature\" is awaiting your review", "timestamp": "2025-07-10T11:02:22.443Z", "isRead": true, "project": {"id": 311, "title": "Lagos Parks Mobile App Development"}, "isFromNetwork": true, "userId": 31}, {"id": "task-104-311", "type": "task_submission", "title": "<PERSON><PERSON><PERSON> submitted a task", "message": "\"Navigation and menu structure\" is awaiting your review", "timestamp": "2025-07-12T11:07:10.530Z", "isRead": false, "project": {"id": 311, "title": "Lagos Parks Mobile App Development"}, "isFromNetwork": true, "userId": 31}, {"id": "task-105-311", "type": "task_submission", "title": "<PERSON><PERSON><PERSON> submitted a task", "message": "\"Push notification system design\" is awaiting your review", "timestamp": "2025-07-09T04:24:39.468Z", "isRead": true, "project": {"id": 311, "title": "Lagos Parks Mobile App Development"}, "isFromNetwork": true, "userId": 31}, {"id": "gig-app-network-5", "type": "gig_application", "title": "<PERSON> applied for Interactive Park Map Web App", "message": "1 network application for \"Interactive Park Map Web App\"", "timestamp": "2025-07-13T10:30:00Z", "isRead": true, "gig": {"id": 5, "title": "Interactive Park Map Web App"}, "isFromNetwork": true, "userId": 29}, {"id": "gig-app-public-5", "type": "gig_application", "title": "<PERSON><PERSON> and 1 other applied for Interactive Park Map Web App", "message": "2 applications for \"Interactive Park Map Web App\"", "timestamp": "2025-07-12T14:15:00Z", "isRead": false, "gig": {"id": 5, "title": "Interactive Park Map Web App"}, "isFromNetwork": false, "userId": 1}, {"id": "project-pause-1", "type": "project_pause", "title": "<PERSON> requested a pause for your Brand Animation Package project", "message": "Motion graphics project needs temporary pause", "timestamp": "2025-07-12T21:06:35.376Z", "isRead": false, "project": {"id": 301, "title": "Brand Animation Package"}, "isFromNetwork": true, "userId": 28}, {"id": "project-accepted-1", "type": "project_accepted", "title": "<PERSON> accepted your project", "message": "UI component library project has been accepted and will start soon", "timestamp": "2025-07-13T21:06:35.376Z", "isRead": true, "project": {"id": 302, "title": "UI Component Library"}, "isFromNetwork": true, "userId": 26}, {"id": "proposal-1", "type": "proposal_sent", "title": "<PERSON> sent you a proposal", "message": "New project proposal for video production services", "timestamp": "2025-07-13T21:06:35.376Z", "isRead": false, "isFromNetwork": true, "userId": 25}, {"id": "invoice-1", "type": "invoice_sent", "title": "<PERSON><PERSON><PERSON> Flether sent you a new invoice", "message": "Invoice MF-24BGJ for Lagos Parks Services website re-design", "timestamp": "2025-07-14T18:06:35.376Z", "isRead": true, "invoice": {"number": "MF-24BGJ", "amount": 5244, "projectTitle": "Lagos Parks Services website re-design"}, "isFromNetwork": true, "userId": 31}, {"id": "gig-app-4", "type": "gig_application", "title": "<PERSON><PERSON> applied for Park Visitor Mobile App", "message": "New application for \"Park Visitor Mobile App\"", "timestamp": "2025-07-12T09:15:00Z", "isRead": false, "gig": {"id": 6, "title": "Park Visitor Mobile App"}, "isFromNetwork": false, "userId": 11}, {"id": "gig-app-5", "type": "gig_application", "title": "<PERSON> applied for Digital Signage Content Management", "message": "New application for \"Digital Signage Content Management\"", "timestamp": "2025-07-13T14:30:00Z", "isRead": true, "gig": {"id": 7, "title": "Digital Signage Content Management"}, "isFromNetwork": false, "userId": 2}, {"id": "invoice-paid-1", "type": "invoice_paid", "title": "Invoice payment processed", "message": "Payment for milestone 2 has been processed successfully", "timestamp": "2025-07-18T14:12:50.466Z", "isRead": true, "project": {"id": 1, "title": "Interactive Park Map Web App"}, "isFromNetwork": false}, {"id": "project-pause-accepted-1", "type": "project_pause_accepted", "title": "Project pause request approved", "message": "Your request to pause the Interactive Park Map project has been approved", "timestamp": "2025-07-18T12:12:50.466Z", "isRead": true, "project": {"id": 1, "title": "Interactive Park Map Web App"}, "isFromNetwork": false}, {"id": "storefront-purchase-1", "type": "storefront_purchase", "title": "Mars<PERSON> Fletcher just purchased Poetry Slam Live tickets", "message": "New sale on your storefront", "timestamp": "2025-07-18T15:12:50.466Z", "isRead": true, "isFromNetwork": false}, {"id": "task-202-314", "type": "task_submission", "title": "<PERSON><PERSON><PERSON> submitted a task", "message": "\"High-fidelity UI design and prototyping\" is awaiting your review", "timestamp": "2025-07-16T19:34:28.815Z", "isRead": true, "user": {"id": 31, "name": "<PERSON><PERSON><PERSON>", "avatar": "/avatars/margsate-flether.png"}, "project": {"id": 314, "title": "E-commerce Platform UI Redesign"}, "isFromNetwork": true}]}, {"commissionerId": 33, "notifications": []}, {"commissionerId": 34, "notifications": []}]